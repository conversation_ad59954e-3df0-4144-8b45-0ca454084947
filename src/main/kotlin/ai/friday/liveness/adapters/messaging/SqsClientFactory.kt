package ai.friday.liveness.adapters.messaging

import io.micronaut.context.annotation.Factory
import io.micronaut.context.annotation.Primary
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.sqs.SqsClient

@Factory
class SqsClientFactory(@Property(name = "aws.region", defaultValue = "us-east-1") private val region: String) {

    @Singleton
    @Primary
    fun sqsClient(): SqsClient = SqsClient.builder().region(Region.of(region)).build()
}